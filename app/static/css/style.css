/* CVE 搜索服务样式文件 */

body {
    margin: 0;
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.app-container {
    min-height: 100vh;
    padding: 20px;
}

.header {
    text-align: center;
    color: white;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 3rem;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2rem;
    margin: 10px 0;
    opacity: 0.9;
}

.search-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

.search-section {
    padding: 40px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.search-input {
    margin-bottom: 20px;
}

.search-input .el-input__inner {
    font-size: 16px;
    padding: 15px 20px;
    border-radius: 12px;
}

.search-input .el-input-group__prepend,
.search-input .el-input-group__append {
    border-radius: 12px;
}

.search-options {
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

.options-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.results-selector {
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-size-buttons {
    display: flex;
    gap: 5px;
}

.examples-toggle {
    display: flex;
    align-items: center;
}

.examples-section {
    padding: 30px 40px;
    border-top: 1px solid #eee;
}

.example-item {
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #409eff;
    cursor: pointer;
    transition: all 0.3s;
}

.example-item:hover {
    background: #e3f2fd;
    transform: translateX(5px);
}

.example-title {
    font-weight: bold;
    color: #409eff;
    margin-bottom: 5px;
}

.example-query {
    font-family: 'Courier New', monospace;
    background: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    margin: 5px 0;
    border: 1px solid #ddd;
}

.results-section {
    padding: 40px;
}

.result-item {
    margin-bottom: 30px;
    padding: 25px;
    border: 1px solid #eee;
    border-radius: 12px;
    background: #fafafa;
    transition: all 0.3s;
}

.result-item:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.cve-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.cve-id {
    font-size: 1.3rem;
    font-weight: bold;
    color: #e74c3c;
}

.cve-date {
    color: #666;
    font-size: 0.9rem;
}

.cve-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #2c3e50;
}

.cve-description {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
}

.cve-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.cve-summary {
    background: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.summary-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    font-weight: bold;
    color: #2e7d32;
}

.summary-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.summary-content {
    line-height: 1.6;
}

.summary-content h1,
.summary-content h2,
.summary-content h3 {
    margin: 10px 0 5px 0;
    color: #2e7d32;
}

.summary-content p {
    margin: 8px 0;
}

.summary-content ul,
.summary-content ol {
    margin: 8px 0;
    padding-left: 20px;
}

.summary-content code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

.summary-content blockquote {
    border-left: 4px solid #4caf50;
    margin: 10px 0;
    padding-left: 15px;
    color: #666;
}

.summary-placeholder {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    text-align: center;
    color: #666;
}

.loading-container {
    text-align: center;
    padding: 40px;
}

.stats-info {
    margin-bottom: 20px;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin: 30px 0;
}

.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: #409eff;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.back-to-top:hover {
    background: #337ecc;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.back-to-top.hidden {
    opacity: 0;
    visibility: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2rem;
    }
    
    .search-section, .results-section {
        padding: 20px;
    }
    
    .search-options {
        flex-direction: column;
        align-items: stretch;
    }
    
    .cve-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .cve-meta {
        flex-direction: column;
        gap: 10px;
    }
}
