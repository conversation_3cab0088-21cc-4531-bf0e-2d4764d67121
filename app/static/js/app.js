// CVE 搜索服务主应用 JavaScript

const { createApp, ref, onMounted, onUnmounted } = Vue;
const { ElMessage, ElNotification } = ElementPlus;

const app = createApp({
    setup() {
        // 响应式数据
        const searchQuery = ref('');
        const pageSize = ref(50);
        const currentPage = ref(1);
        const pageSizeOptions = ref([10, 20, 50, 100]);
        const searching = ref(false);
        const hasSearched = ref(false);
        const searchResults = ref(null);
        const showExamples = ref(true);
        const examples = ref([]);
        const loadingSummaries = ref({});
        const showBackToTop = ref(false);

        // 加载搜索示例
        const loadExamples = async () => {
            try {
                const response = await fetch('/api/examples');
                const data = await response.json();
                examples.value = data.examples;
            } catch (error) {
                console.error('加载示例失败:', error);
            }
        };

        // 执行搜索
        const performSearch = async (resetPage = true) => {
            if (!searchQuery.value.trim()) {
                ElMessage.warning('请输入搜索条件');
                return;
            }

            if (resetPage) {
                currentPage.value = 1;
            }

            searching.value = true;
            hasSearched.value = true;
            
            // 搜索后自动收起示例
            if (resetPage) {
                showExamples.value = false;
            }

            try {
                const params = new URLSearchParams({
                    query: searchQuery.value,
                    page: currentPage.value,
                    page_size: pageSize.value
                });

                const response = await fetch(`/api/search?${params}`);
                
                if (!response.ok) {
                    throw new Error(`搜索失败: ${response.statusText}`);
                }

                const data = await response.json();
                searchResults.value = data;

                if (resetPage) {
                    ElNotification({
                        title: '搜索完成',
                        message: `找到 ${data.total_results} 个结果`,
                        type: 'success',
                        duration: 3000
                    });
                }

            } catch (error) {
                console.error('搜索错误:', error);
                ElMessage.error(`搜索失败: ${error.message}`);
            } finally {
                searching.value = false;
            }
        };

        // 使用示例查询
        const useExample = (query) => {
            searchQuery.value = query;
            showExamples.value = false;
        };

        // 切换示例显示
        const toggleExamples = () => {
            showExamples.value = !showExamples.value;
        };

        // 改变页面大小
        const changePageSize = (newSize) => {
            pageSize.value = newSize;
            currentPage.value = 1;
            if (hasSearched.value && searchQuery.value.trim()) {
                performSearch(false);
            }
        };

        // 处理页面变化
        const handlePageChange = (page) => {
            currentPage.value = page;
            performSearch(false);
            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        };

        // 获取显示范围
        const getDisplayRange = () => {
            if (!searchResults.value) return '';
            const start = (searchResults.value.page - 1) * searchResults.value.page_size + 1;
            const end = Math.min(start + searchResults.value.results.length - 1, searchResults.value.total_results);
            return `${start}-${end}`;
        };

        // 回到顶部
        const scrollToTop = () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        };

        // 监听滚动事件
        const handleScroll = () => {
            showBackToTop.value = window.scrollY > 300;
        };

        // 生成 AI 总结
        const generateSummary = async (cveId) => {
            loadingSummaries.value[cveId] = true;
            
            try {
                const response = await fetch(`/api/summary/${cveId}`);
                
                if (!response.ok) {
                    throw new Error(`生成总结失败: ${response.statusText}`);
                }
                
                const summary = await response.json();
                
                // 更新搜索结果中的总结
                if (searchResults.value && searchResults.value.results) {
                    const result = searchResults.value.results.find(r => r.cve_info.cve_id === cveId);
                    if (result) {
                        result.summary = summary;
                    }
                }
                
                ElMessage.success('AI 总结生成成功');
                
            } catch (error) {
                console.error('生成总结错误:', error);
                ElMessage.error(`生成总结失败: ${error.message}`);
            } finally {
                loadingSummaries.value[cveId] = false;
            }
        };

        // 刷新 AI 总结
        const refreshSummary = async (cveId) => {
            loadingSummaries.value[cveId] = true;
            
            try {
                const response = await fetch(`/api/summary/${cveId}?force_refresh=true`);
                
                if (!response.ok) {
                    throw new Error(`刷新总结失败: ${response.statusText}`);
                }
                
                const summary = await response.json();
                
                // 更新搜索结果中的总结
                if (searchResults.value && searchResults.value.results) {
                    const result = searchResults.value.results.find(r => r.cve_info.cve_id === cveId);
                    if (result) {
                        result.summary = summary;
                    }
                }
                
                ElMessage.success('AI 总结已刷新');
                
            } catch (error) {
                console.error('刷新总结错误:', error);
                ElMessage.error(`刷新总结失败: ${error.message}`);
            } finally {
                loadingSummaries.value[cveId] = false;
            }
        };

        // 渲染 Markdown
        const renderMarkdown = (text) => {
            if (!text) return '';
            try {
                // 配置 marked 选项
                marked.setOptions({
                    breaks: true,
                    gfm: true
                });
                
                const html = marked.parse(text);
                // 使用 DOMPurify 清理 HTML
                return DOMPurify.sanitize(html);
            } catch (error) {
                console.error('Markdown 渲染错误:', error);
                // 如果渲染失败，返回纯文本
                return text.replace(/\n/g, '<br>');
            }
        };

        // 格式化日期
        const formatDate = (dateStr) => {
            if (!dateStr) return '未知';
            try {
                return new Date(dateStr).toLocaleDateString('zh-CN');
            } catch {
                return dateStr;
            }
        };

        // 截断文本
        const truncateText = (text, maxLength) => {
            if (!text) return '';
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        };

        // 获取严重程度类型
        const getSeverityType = (severity) => {
            const severityMap = {
                'CRITICAL': 'danger',
                'HIGH': 'danger',
                'MEDIUM': 'warning',
                'LOW': 'info'
            };
            return severityMap[severity?.toUpperCase()] || 'info';
        };

        // 组件挂载时加载示例
        onMounted(() => {
            loadExamples();
            // 没有搜索时显示示例
            showExamples.value = !hasSearched.value;
            // 添加滚动监听
            window.addEventListener('scroll', handleScroll);
        });

        // 组件卸载时清理监听器
        onUnmounted(() => {
            window.removeEventListener('scroll', handleScroll);
        });

        return {
            searchQuery,
            pageSize,
            currentPage,
            pageSizeOptions,
            searching,
            hasSearched,
            searchResults,
            showExamples,
            examples,
            loadingSummaries,
            showBackToTop,
            performSearch,
            useExample,
            toggleExamples,
            changePageSize,
            handlePageChange,
            getDisplayRange,
            generateSummary,
            refreshSummary,
            renderMarkdown,
            scrollToTop,
            formatDate,
            truncateText,
            getSeverityType
        };
    }
});

// 挂载应用
app.use(ElementPlus).mount('#app');
