// CVE 搜索服务主应用 JavaScript

const { createApp, ref, onMounted, onUnmounted } = Vue;
const { ElMessage, ElNotification } = ElementPlus;

const app = createApp({
    setup() {
        // 响应式数据
        const searchQuery = ref('');
        const pageSize = ref(50);
        const currentPage = ref(1);
        const pageSizeOptions = ref([10, 20, 50, 100]);
        const searching = ref(false);
        const hasSearched = ref(false);
        const searchResults = ref(null);
        const showExamples = ref(true);
        const showFilters = ref(false);
        const examples = ref([]);
        const loadingSummaries = ref({});
        const showBackToTop = ref(false);

        // 筛选器相关数据
        const selectedFilters = ref({
            vulnerability: [],
            software: [],
            language: [],
            severity: []
        });

        const expandedGroups = ref({
            vulnerability: true,
            software: false,
            language: false,
            severity: false
        });

        const filterOptions = ref({
            vulnerability: [
                { label: '缓冲区溢出', value: 'buffer_overflow', description: '包含堆栈或堆缓冲区溢出漏洞' },
                { label: 'SQL 注入', value: 'sql_injection', description: '包含 SQL 注入相关漏洞' },
                { label: '任意代码执行', value: 'code_execution', description: '包含远程代码执行漏洞' },
                { label: '任意文件读取', value: 'file_access', description: '包含路径遍历和文件包含漏洞' },
                { label: '跨站脚本(XSS)', value: 'xss', description: '包含跨站脚本攻击漏洞' },
                { label: '跨站请求伪造(CSRF)', value: 'csrf', description: '包含跨站请求伪造漏洞' },
                { label: '权限提升', value: 'privilege_escalation', description: '包含权限提升漏洞' },
                { label: '信息泄露', value: 'information_disclosure', description: '包含信息泄露漏洞' }
            ],
            software: [
                { label: '服务器软件', value: 'server', description: '包含 Apache、Nginx、Tomcat、IIS 等服务器软件' },
                { label: '数据库', value: 'database', description: '包含 MySQL、PostgreSQL、MongoDB 等数据库' },
                { label: '操作系统', value: 'os', description: '包含 Windows、Linux、macOS 等操作系统' },
                { label: '浏览器', value: 'browser', description: '包含 Chrome、Firefox、Safari、Edge 等浏览器' }
            ],
            language: [
                { label: 'PHP', value: 'php', description: '包含 PHP 相关漏洞' },
                { label: 'Java', value: 'java', description: '包含 Java 相关漏洞' },
                { label: 'Python', value: 'python', description: '包含 Python 相关漏洞' },
                { label: 'JavaScript', value: 'javascript', description: '包含 JavaScript 相关漏洞' },
                { label: 'C/C++', value: 'c_cpp', description: '包含 C/C++ 相关漏洞' },
                { label: '.NET', value: 'dotnet', description: '包含 .NET 相关漏洞' }
            ],
            severity: [
                { label: '严重 (Critical)', value: 'critical', description: '严重程度为 Critical 的漏洞' },
                { label: '高危 (High)', value: 'high', description: '严重程度为 High 的漏洞' },
                { label: '中危 (Medium)', value: 'medium', description: '严重程度为 Medium 的漏洞' },
                { label: '低危 (Low)', value: 'low', description: '严重程度为 Low 的漏洞' }
            ]
        });

        // 加载搜索示例
        const loadExamples = async () => {
            try {
                const response = await fetch('/api/examples');
                const data = await response.json();
                examples.value = data.examples;
            } catch (error) {
                console.error('加载示例失败:', error);
            }
        };

        // 构建筛选器查询
        const buildFilterQuery = () => {
            const filterQueries = [];

            // 漏洞类型筛选
            if (selectedFilters.value.vulnerability.length > 0) {
                const vulnQueries = [];
                selectedFilters.value.vulnerability.forEach(filter => {
                    switch (filter) {
                        case 'buffer_overflow':
                            vulnQueries.push('+(heap|stack)_buffer_overflow, +"heap-buffer-overflow", +"stack-buffer-overflow"');
                            break;
                        case 'sql_injection':
                            vulnQueries.push('+sql injection, +sql_injection, +SQLi');
                            break;
                        case 'code_execution':
                            vulnQueries.push('+remote code execution, +remote_code_execution, +RCE');
                            break;
                        case 'file_access':
                            vulnQueries.push('+path traversal, +path_traversal, +LFI, +RFI');
                            break;
                        case 'xss':
                            vulnQueries.push('+cross site scripting, +cross-site-scripting, +XSS');
                            break;
                        case 'csrf':
                            vulnQueries.push('+cross site request forgery, +cross-site-request-forgery, +CSRF');
                            break;
                        case 'privilege_escalation':
                            vulnQueries.push('+privilege escalation, +privilege_escalation, +elevation');
                            break;
                        case 'information_disclosure':
                            vulnQueries.push('+information disclosure, +information_disclosure, +data leak');
                            break;
                    }
                });
                if (vulnQueries.length > 0) {
                    filterQueries.push(`(${vulnQueries.join(' | ')})`);
                }
            }

            // 软件类型筛选
            if (selectedFilters.value.software.length > 0) {
                const softwareQueries = [];
                selectedFilters.value.software.forEach(filter => {
                    switch (filter) {
                        case 'server':
                            softwareQueries.push('+apache, +nginx, +tomcat, +iis');
                            break;
                        case 'database':
                            softwareQueries.push('+mysql, +mariadb, +postgresql, +mongodb');
                            break;
                        case 'os':
                            softwareQueries.push('+windows, +linux, +macos, +unix');
                            break;
                        case 'browser':
                            softwareQueries.push('+chrome, +firefox, +safari, +edge, +webkit');
                            break;
                    }
                });
                if (softwareQueries.length > 0) {
                    filterQueries.push(`(${softwareQueries.join(' | ')})`);
                }
            }

            // 开发语言筛选
            if (selectedFilters.value.language.length > 0) {
                const langQueries = [];
                selectedFilters.value.language.forEach(filter => {
                    switch (filter) {
                        case 'php':
                            langQueries.push('+php');
                            break;
                        case 'java':
                            langQueries.push('+java');
                            break;
                        case 'python':
                            langQueries.push('+python');
                            break;
                        case 'javascript':
                            langQueries.push('+javascript, +nodejs, +node.js');
                            break;
                        case 'c_cpp':
                            langQueries.push('+c++, +cpp, +"c programming"');
                            break;
                        case 'dotnet':
                            langQueries.push('+.net, +dotnet, +asp.net');
                            break;
                    }
                });
                if (langQueries.length > 0) {
                    filterQueries.push(`(${langQueries.join(' | ')})`);
                }
            }

            // 严重程度筛选
            if (selectedFilters.value.severity.length > 0) {
                const severityQueries = [];
                selectedFilters.value.severity.forEach(filter => {
                    switch (filter) {
                        case 'critical':
                            severityQueries.push('+critical');
                            break;
                        case 'high':
                            severityQueries.push('+high');
                            break;
                        case 'medium':
                            severityQueries.push('+medium');
                            break;
                        case 'low':
                            severityQueries.push('+low');
                            break;
                    }
                });
                if (severityQueries.length > 0) {
                    filterQueries.push(`(${severityQueries.join(' | ')})`);
                }
            }

            return filterQueries.join(', ');
        };

        // 执行搜索
        const performSearch = async (resetPage = true) => {
            const userQuery = searchQuery.value.trim();
            const filterQuery = buildFilterQuery();

            // 如果没有用户查询且没有筛选条件，提示用户
            if (!userQuery && !filterQuery) {
                ElMessage.warning('请输入搜索条件或选择筛选条件');
                return;
            }

            if (resetPage) {
                currentPage.value = 1;
            }

            searching.value = true;
            hasSearched.value = true;

            // 搜索后自动收起示例和筛选器
            if (resetPage) {
                showExamples.value = false;
                showFilters.value = false;
            }

            try {
                // 构建完整查询
                let fullQuery = '';
                if (userQuery && filterQuery) {
                    fullQuery = `${userQuery}, ${filterQuery}`;
                } else if (userQuery) {
                    fullQuery = userQuery;
                } else {
                    fullQuery = filterQuery;
                }

                const params = new URLSearchParams({
                    query: fullQuery,
                    page: currentPage.value,
                    page_size: pageSize.value
                });

                const response = await fetch(`/api/search?${params}`);

                if (!response.ok) {
                    throw new Error(`搜索失败: ${response.statusText}`);
                }

                const data = await response.json();
                searchResults.value = data;

                if (resetPage) {
                    ElNotification({
                        title: '搜索完成',
                        message: `找到 ${data.total_results} 个结果`,
                        type: 'success',
                        duration: 3000
                    });
                }

            } catch (error) {
                console.error('搜索错误:', error);
                ElMessage.error(`搜索失败: ${error.message}`);
            } finally {
                searching.value = false;
            }
        };

        // 使用示例查询
        const useExample = (query) => {
            searchQuery.value = query;
            showExamples.value = false;
        };

        // 切换示例显示
        const toggleExamples = () => {
            showExamples.value = !showExamples.value;
        };

        // 切换筛选器显示
        const toggleFilters = () => {
            showFilters.value = !showFilters.value;
        };

        // 切换筛选器组展开状态
        const toggleFilterGroup = (groupName) => {
            expandedGroups.value[groupName] = !expandedGroups.value[groupName];
        };

        // 获取选中筛选器总数
        const getSelectedFiltersCount = () => {
            return Object.values(selectedFilters.value).reduce((total, group) => total + group.length, 0);
        };

        // 获取特定组选中数量
        const getGroupSelectedCount = (groupName) => {
            return selectedFilters.value[groupName]?.length || 0;
        };

        // 清空所有筛选器
        const clearAllFilters = () => {
            selectedFilters.value = {
                vulnerability: [],
                software: [],
                language: [],
                severity: []
            };
        };

        // 改变页面大小
        const changePageSize = (newSize) => {
            pageSize.value = newSize;
            currentPage.value = 1;
            if (hasSearched.value && searchQuery.value.trim()) {
                performSearch(false);
            }
        };

        // 处理页面变化
        const handlePageChange = (page) => {
            currentPage.value = page;
            performSearch(false);
            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        };

        // 获取显示范围
        const getDisplayRange = () => {
            if (!searchResults.value) return '';
            const start = (searchResults.value.page - 1) * searchResults.value.page_size + 1;
            const end = Math.min(start + searchResults.value.results.length - 1, searchResults.value.total_results);
            return `${start}-${end}`;
        };

        // 回到顶部
        const scrollToTop = () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        };

        // 监听滚动事件
        const handleScroll = () => {
            showBackToTop.value = window.scrollY > 300;
        };

        // 生成 AI 总结
        const generateSummary = async (cveId) => {
            loadingSummaries.value[cveId] = true;
            
            try {
                const response = await fetch(`/api/summary/${cveId}`);
                
                if (!response.ok) {
                    throw new Error(`生成总结失败: ${response.statusText}`);
                }
                
                const summary = await response.json();
                
                // 更新搜索结果中的总结
                if (searchResults.value && searchResults.value.results) {
                    const result = searchResults.value.results.find(r => r.cve_info.cve_id === cveId);
                    if (result) {
                        result.summary = summary;
                    }
                }
                
                ElMessage.success('AI 总结生成成功');
                
            } catch (error) {
                console.error('生成总结错误:', error);
                ElMessage.error(`生成总结失败: ${error.message}`);
            } finally {
                loadingSummaries.value[cveId] = false;
            }
        };

        // 刷新 AI 总结
        const refreshSummary = async (cveId) => {
            loadingSummaries.value[cveId] = true;
            
            try {
                const response = await fetch(`/api/summary/${cveId}?force_refresh=true`);
                
                if (!response.ok) {
                    throw new Error(`刷新总结失败: ${response.statusText}`);
                }
                
                const summary = await response.json();
                
                // 更新搜索结果中的总结
                if (searchResults.value && searchResults.value.results) {
                    const result = searchResults.value.results.find(r => r.cve_info.cve_id === cveId);
                    if (result) {
                        result.summary = summary;
                    }
                }
                
                ElMessage.success('AI 总结已刷新');
                
            } catch (error) {
                console.error('刷新总结错误:', error);
                ElMessage.error(`刷新总结失败: ${error.message}`);
            } finally {
                loadingSummaries.value[cveId] = false;
            }
        };

        // 渲染 Markdown
        const renderMarkdown = (text) => {
            if (!text) return '';
            try {
                // 配置 marked 选项
                marked.setOptions({
                    breaks: true,
                    gfm: true
                });
                
                const html = marked.parse(text);
                // 使用 DOMPurify 清理 HTML
                return DOMPurify.sanitize(html);
            } catch (error) {
                console.error('Markdown 渲染错误:', error);
                // 如果渲染失败，返回纯文本
                return text.replace(/\n/g, '<br>');
            }
        };

        // 格式化日期
        const formatDate = (dateStr) => {
            if (!dateStr) return '未知';
            try {
                return new Date(dateStr).toLocaleDateString('zh-CN');
            } catch {
                return dateStr;
            }
        };

        // 截断文本
        const truncateText = (text, maxLength) => {
            if (!text) return '';
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        };

        // 获取严重程度类型
        const getSeverityType = (severity) => {
            const severityMap = {
                'CRITICAL': 'danger',
                'HIGH': 'danger',
                'MEDIUM': 'warning',
                'LOW': 'info'
            };
            return severityMap[severity?.toUpperCase()] || 'info';
        };

        // 组件挂载时加载示例
        onMounted(() => {
            loadExamples();
            // 没有搜索时显示示例
            showExamples.value = !hasSearched.value;
            // 添加滚动监听
            window.addEventListener('scroll', handleScroll);
        });

        // 组件卸载时清理监听器
        onUnmounted(() => {
            window.removeEventListener('scroll', handleScroll);
        });

        return {
            searchQuery,
            pageSize,
            currentPage,
            pageSizeOptions,
            searching,
            hasSearched,
            searchResults,
            showExamples,
            showFilters,
            examples,
            loadingSummaries,
            showBackToTop,
            selectedFilters,
            expandedGroups,
            filterOptions,
            performSearch,
            useExample,
            toggleExamples,
            toggleFilters,
            toggleFilterGroup,
            getSelectedFiltersCount,
            getGroupSelectedCount,
            clearAllFilters,
            changePageSize,
            handlePageChange,
            getDisplayRange,
            generateSummary,
            refreshSummary,
            renderMarkdown,
            scrollToTop,
            formatDate,
            truncateText,
            getSeverityType
        };
    }
});

// 挂载应用
app.use(ElementPlus).mount('#app');
