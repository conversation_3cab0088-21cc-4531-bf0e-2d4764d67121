<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CVE 搜索服务</title>

    <!-- 外部依赖 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <script src="https://unpkg.com/marked/marked.min.js"></script>
    <script src="https://unpkg.com/dompurify@2.4.7/dist/purify.min.js"></script>

    <!-- 本地样式 -->
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 头部 -->
            <div class="header">
                <h1>🔍 CVE 搜索服务</h1>
                <p>基于 AI 的漏洞信息搜索与总结平台</p>
            </div>
            
            <!-- 搜索容器 -->
            <div class="search-container">
                <!-- 搜索区域 -->
                <div class="search-section">
                    <el-input
                        v-model="searchQuery"
                        placeholder="输入搜索条件，例如：remote code execution"
                        size="large"
                        class="search-input"
                        @keyup.enter="performSearch"
                        clearable
                    >
                        <template #prepend>
                            <el-icon><Search /></el-icon>
                        </template>
                        <template #append>
                            <el-button
                                type="primary"
                                @click="performSearch"
                                :loading="searching"
                                size="large"
                            >
                                搜索
                            </el-button>
                        </template>
                    </el-input>

                    <div class="search-options">
                        <div class="options-row">
                            <div class="results-selector">
                                <span>每页显示：</span>
                                <el-button-group class="page-size-buttons">
                                    <el-button
                                        v-for="size in pageSizeOptions"
                                        :key="size"
                                        :type="pageSize === size ? 'primary' : 'default'"
                                        size="small"
                                        @click="changePageSize(size)"
                                    >
                                        {{ size }}
                                    </el-button>
                                </el-button-group>
                            </div>

                            <div class="examples-toggle">
                                <el-button
                                    size="small"
                                    @click="toggleExamples"
                                    :type="showExamples ? 'primary' : 'default'"
                                    :icon="showExamples ? 'ArrowUp' : 'ArrowDown'"
                                >
                                    {{ showExamples ? '隐藏' : '显示' }}搜索示例
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 搜索示例 -->
                <div v-if="showExamples" class="examples-section">
                    <h3>🎯 搜索语法示例</h3>
                    <div 
                        v-for="example in examples" 
                        :key="example.title"
                        class="example-item"
                        @click="useExample(example.query)"
                    >
                        <div class="example-title">{{ example.title }}</div>
                        <div class="example-query">{{ example.query }}</div>
                        <div style="color: #666; font-size: 0.9rem;">{{ example.description }}</div>
                    </div>
                </div>
                
                <!-- 搜索结果 -->
                <div v-if="hasSearched" class="results-section">
                    <!-- 加载状态 -->
                    <div v-if="searching" class="loading-container">
                        <el-icon class="is-loading" size="40"><Loading /></el-icon>
                        <p>正在搜索中...</p>
                    </div>
                    
                    <!-- 搜索统计 -->
                    <div v-else-if="searchResults" class="stats-info">
                        <el-row justify="space-between" align="middle">
                            <el-col :span="16">
                                <el-space>
                                    <el-tag type="success" size="large">
                                        <el-icon><DocumentChecked /></el-icon>
                                        共找到 {{ searchResults.total_results }} 个 CVE
                                    </el-tag>
                                    <el-tag type="info">
                                        第 {{ searchResults.page }} / {{ searchResults.total_pages }} 页
                                    </el-tag>
                                    <el-tag type="warning">
                                        显示 {{ getDisplayRange() }}
                                    </el-tag>
                                </el-space>
                            </el-col>
                            <el-col :span="8" style="text-align: right;">
                                <el-tag type="info">
                                    搜索耗时: {{ searchResults.search_time.toFixed(2) }} 秒
                                </el-tag>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 顶部分页 -->
                    <div v-if="searchResults && searchResults.total_pages > 1" class="pagination-container">
                        <el-pagination
                            v-model:current-page="currentPage"
                            :page-size="pageSize"
                            :total="searchResults.total_results"
                            :page-count="searchResults.total_pages"
                            layout="prev, pager, next, jumper"
                            @current-change="handlePageChange"
                            background
                        />
                    </div>
                    
                    <!-- 结果列表 -->
                    <div v-if="searchResults && searchResults.results.length > 0">
                        <div 
                            v-for="result in searchResults.results" 
                            :key="result.cve_info.cve_id"
                            class="result-item"
                        >
                            <!-- CVE 头部信息 -->
                            <div class="cve-header">
                                <span class="cve-id">{{ result.cve_info.cve_id }}</span>
                                <span class="cve-date">{{ formatDate(result.cve_info.published_date) }}</span>
                            </div>
                            
                            <!-- CVE 标题 -->
                            <div v-if="result.cve_info.title" class="cve-title">
                                {{ result.cve_info.title }}
                            </div>
                            
                            <!-- CVE 描述 -->
                            <div v-if="result.cve_info.description" class="cve-description">
                                {{ truncateText(result.cve_info.description, 300) }}
                            </div>
                            
                            <!-- CVE 元信息 -->
                            <div class="cve-meta">
                                <div v-if="result.cve_info.severity" class="meta-item">
                                    <el-tag :type="getSeverityType(result.cve_info.severity)">
                                        {{ result.cve_info.severity }}
                                    </el-tag>
                                </div>
                                
                                <div v-if="result.cve_info.cvss_score" class="meta-item">
                                    <el-tag type="warning">
                                        CVSS: {{ result.cve_info.cvss_score }}
                                    </el-tag>
                                </div>
                                
                                <div v-if="result.cve_info.affected_products.length > 0" class="meta-item">
                                    <el-tag type="info">
                                        影响产品: {{ result.cve_info.affected_products.slice(0, 3).join(', ') }}
                                        <span v-if="result.cve_info.affected_products.length > 3">...</span>
                                    </el-tag>
                                </div>
                            </div>
                            
                            <!-- AI 总结区域 -->
                            <div v-if="result.summary" class="cve-summary">
                                <div class="summary-header">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <el-icon><ChatDotRound /></el-icon>
                                        AI 总结
                                        <el-tag size="small" type="success">{{ result.summary.model_used }}</el-tag>
                                    </div>
                                    <div class="summary-actions">
                                        <el-button
                                            size="small"
                                            type="primary"
                                            @click="refreshSummary(result.cve_info.cve_id)"
                                            :loading="loadingSummaries[result.cve_info.cve_id]"
                                        >
                                            刷新总结
                                        </el-button>
                                    </div>
                                </div>
                                <div class="summary-content" v-html="renderMarkdown(result.summary.summary)"></div>
                            </div>

                            <!-- 无总结时显示生成按钮 -->
                            <div v-else class="summary-placeholder">
                                <p>暂无 AI 总结</p>
                                <el-button
                                    type="primary"
                                    @click="generateSummary(result.cve_info.cve_id)"
                                    :loading="loadingSummaries[result.cve_info.cve_id]"
                                >
                                    <el-icon><ChatDotRound /></el-icon>
                                    生成 AI 总结
                                </el-button>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <div v-if="searchResults.total_pages > 1" class="pagination-container">
                            <el-pagination
                                v-model:current-page="currentPage"
                                :page-size="pageSize"
                                :total="searchResults.total_results"
                                :page-count="searchResults.total_pages"
                                layout="prev, pager, next, jumper"
                                @current-change="handlePageChange"
                                background
                            />
                        </div>
                    </div>

                    <!-- 无结果 -->
                    <div v-else-if="searchResults && searchResults.results.length === 0">
                        <el-empty description="未找到匹配的 CVE">
                            <el-button type="primary" @click="showExamples = true">查看搜索示例</el-button>
                        </el-empty>
                    </div>
                </div>
            </div>
        </div>

        <!-- 回到顶部按钮 -->
        <button
            class="back-to-top"
            :class="{ hidden: !showBackToTop }"
            @click="scrollToTop"
            title="回到顶部"
        >
            <el-icon><ArrowUp /></el-icon>
        </button>
    </div>

    <!-- 本地脚本 -->
    <script src="/static/js/app.js"></script>
</body>
</html>
