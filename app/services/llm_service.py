"""
LLM 服务模块
负责使用 LiteLLM 调用 DeepSeek 模型生成 CVE 总结
"""

import asyncio
from datetime import datetime
from typing import Optional
import litellm
from litellm import acompletion

from ..core.config import settings
from ..models.schemas import CVEInfo, CVESummary


class LLMService:
    """LLM 服务类"""
    
    def __init__(self):
        self.base_url = settings.llm.base_url
        self.api_key = settings.llm.api_key
        self.model = settings.llm.model
        self.timeout = settings.llm.timeout
        self.max_retries = settings.llm.max_retries
        
        # 配置 LiteLLM
        litellm.api_base = self.base_url
        litellm.api_key = self.api_key
    
    async def generate_summary(self, cve_info: CVEInfo) -> Optional[CVESummary]:
        """
        生成 CVE 总结
        
        Args:
            cve_info: CVE 信息对象
            
        Returns:
            CVESummary: 生成的总结，如果失败返回 None
        """
        try:
            # 构建提示词
            prompt = self._build_prompt(cve_info)
            
            # 调用 LLM
            response = await self._call_llm(prompt)
            
            if response:
                return CVESummary(
                    cve_id=cve_info.cve_id,
                    summary=response,
                    generated_at=datetime.utcnow(),
                    model_used=self.model
                )
            
            return None
            
        except Exception as e:
            print(f"生成 CVE {cve_info.cve_id} 总结时出错: {e}")
            return None
    
    def _build_prompt(self, cve_info: CVEInfo) -> str:
        """构建 LLM 提示词"""
        prompt = f"""请对以下 CVE 漏洞信息进行专业总结，要求简洁明了，突出重点：

CVE 编号: {cve_info.cve_id}
"""
        
        if cve_info.title:
            prompt += f"标题: {cve_info.title}\n"
        
        if cve_info.description:
            prompt += f"描述: {cve_info.description}\n"
        
        if cve_info.severity:
            prompt += f"严重程度: {cve_info.severity}\n"
        
        if cve_info.cvss_score:
            prompt += f"CVSS 评分: {cve_info.cvss_score}\n"
        
        if cve_info.affected_products:
            prompt += f"受影响产品: {', '.join(cve_info.affected_products[:5])}\n"  # 限制产品数量
        
        if cve_info.published_date:
            prompt += f"发布日期: {cve_info.published_date}\n"
        
        prompt += """
请用中文总结这个漏洞的关键信息，包括：
1. 漏洞的本质和影响
2. 受影响的系统或软件
3. 潜在的安全风险
4. 建议的防护措施（如果有相关信息）

总结应该控制在 200 字以内，语言简洁专业。"""
        
        return prompt
    
    async def _call_llm(self, prompt: str) -> Optional[str]:
        """
        调用 LLM API
        
        Args:
            prompt: 提示词
            
        Returns:
            str: LLM 响应内容，失败返回 None
        """
        for attempt in range(self.max_retries):
            try:
                print(f"调用 LLM (尝试 {attempt + 1}/{self.max_retries})")
                
                response = await acompletion(
                    model=self.model,
                    messages=[
                        {
                            "role": "system",
                            "content": "你是一个网络安全专家，专门分析和总结 CVE 漏洞信息。"
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    api_base=self.base_url,
                    api_key=self.api_key,
                    timeout=self.timeout,
                    temperature=0.3,
                    max_tokens=500
                )
                
                if response and response.choices:
                    content = response.choices[0].message.content
                    if content:
                        return content.strip()
                
                print(f"LLM 响应为空，尝试 {attempt + 1} 失败")
                
            except Exception as e:
                raise e
                print(f"LLM 调用失败 (尝试 {attempt + 1}): {e}")
                
                if attempt < self.max_retries - 1:
                    # 等待后重试
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                else:
                    print(f"LLM 调用最终失败，已重试 {self.max_retries} 次")
        
        return None
    
    def is_configured(self) -> bool:
        """检查 LLM 服务是否已正确配置"""
        return (
            self.api_key != "your-deepseek-api-key-here" and
            self.api_key.strip() != "" and
            self.base_url.strip() != ""
        )
